describe('Homepage Layout', () => {
  beforeEach(() => {
    cy.visit('http://localhost:3000/')
  })

  it('renders all main sections', () => {
    // Check that all the main page sections exist
    cy.get('.grid-in-hero').should('exist')
    cy.get('.grid-in-roadblocks').should('exist')
    cy.get('.grid-in-ascension-logs').should('exist')
    cy.get('.grid-in-application-development-intro').should('exist')
    cy.get('.grid-in-cards-plan').should('exist')
    cy.get('.grid-in-service-accordion').should('exist')
  })

  it('renders carousel section with correct titles', () => {
    // Check that the carousel section exists and is visible
    cy.get('.grid-in-carousel').should('exist')

    // Check that all three carousel h2 titles exist within the carousel section
    cy.get('.grid-in-carousel h2').should('have.length', 3)
    cy.get('.grid-in-carousel h2').should('contain.text', 'What we love to do:')
    cy.get('.grid-in-carousel h2').should('contain.text', 'Building Dreams The Manystack Way')
    cy.get('.grid-in-carousel h2').should('contain.text', 'Dreams Already Crafted')
  })
})
