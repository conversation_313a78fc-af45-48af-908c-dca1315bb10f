describe('Contact Forms', () => {
  beforeEach(() => {
    cy.visit('http://localhost:3000/')
  })

  describe('Hero Section Forms', () => {
    it('opens EmailSubscriptionForm when clicking "Chart your concept!"', () => {
      cy.get('.grid-in-hero').contains('Chart your concept!').click()
      cy.get('[role="dialog"]').should('be.visible')
      cy.get('[role="dialog"]').should('contain.text', 'Need a Spark of Inspiration?')

      // Close dialog
      cy.get('body').type('{esc}')
      cy.get('[role="dialog"]').should('not.exist')
    })

    it('opens ContactForm when clicking "Drop a line to start!"', () => {
      cy.get('.grid-in-hero').contains('Drop a line to start!').click()
      cy.get('[role="dialog"]').should('be.visible')
      cy.get('[role="dialog"]').should('contain.text', "Let's Build It!")

      // Close dialog
      cy.get('body').type('{esc}')
      cy.get('[role="dialog"]').should('not.exist')
    })
  })

  describe('Carousel Section Forms', () => {
    it('opens ContactForm when clicking "Drop a line to start!" in ClientFeedbackCarouselCard', () => {
      cy.get('.grid-in-carousel').children().first().contains('Drop a line to start!').click()
      cy.get('[role="dialog"]').should('be.visible')
      cy.get('[role="dialog"]').should('contain.text', "Let's Build It!")

      // Close dialog
      cy.get('body').type('{esc}')
      cy.get('[role="dialog"]').should('not.exist')
    })

    it('opens ContactForm when clicking "Drop a line to start!" in ProjectCarouselCard', () => {
      cy.get('.grid-in-carousel').children().last().contains('Drop a line to start!').click()
      cy.get('[role="dialog"]').should('be.visible')
      cy.get('[role="dialog"]').should('contain.text', "Let's Build It!")

      // Close dialog
      cy.get('body').type('{esc}')
      cy.get('[role="dialog"]').should('not.exist')
    })
    it('opens EmailSubscriptionForm when clicking "Chart your concept!" in ProjectCarouselCard', () => {
      cy.get('.grid-in-carousel').contains('Chart your concept!').click()
      cy.get('[role="dialog"]').should('be.visible')
      cy.get('[role="dialog"]').should('contain.text', 'Need a Spark of Inspiration?')

      // Close dialog
      cy.get('body').type('{esc}')
      cy.get('[role="dialog"]').should('not.exist')
    })
  })

  describe('Roadblocks Section Forms', () => {
    it('opens ContactForm when clicking "Write what bugs you most on the road to making your dream a reality here!"', () => {
      cy.get('.grid-in-roadblocks')
        .contains('Write what bugs you most on the road to making your dream a reality here!')
        .click()
      cy.get('[role="dialog"]').should('be.visible')
      cy.get('[role="dialog"]').should('contain.text', "Let's Build It!")

      // Close dialog
      cy.get('body').type('{esc}')
      cy.get('[role="dialog"]').should('not.exist')
    })

    it('opens ChallengeForm when clicking "Add my challenge!"', () => {
      cy.get('.grid-in-roadblocks').contains('Add my challenge!').click()
      cy.get('[role="dialog"]').should('be.visible')
      cy.get('[role="dialog"]').should('contain.text', 'We Care About Your Toughest Challenges')

      // Close dialog
      cy.get('body').type('{esc}')
      cy.get('[role="dialog"]').should('not.exist')
    })

    it('opens ContactForm when clicking "Drop a line to start!"', () => {
      cy.get('.grid-in-roadblocks').contains('Drop a line to start!').click()
      cy.get('[role="dialog"]').should('be.visible')
      cy.get('[role="dialog"]').should('contain.text', "Let's Build It!")

      // Close dialog
      cy.get('body').type('{esc}')
      cy.get('[role="dialog"]').should('not.exist')
    })
  })

  describe('Ascension Logs Section Forms', () => {
    it('opens ContactForm when clicking "Drop a line to start!"', () => {
      cy.get('.grid-in-ascension-logs').contains('Drop a line to start!').click()
      cy.get('[role="dialog"]').should('be.visible')
      cy.get('[role="dialog"]').should('contain.text', "Let's Build It!")

      // Close dialog
      cy.get('body').type('{esc}')
      cy.get('[role="dialog"]').should('not.exist')
    })

    it('opens EmailSubscriptionForm when clicking "chart your concept"', () => {
      cy.get('.grid-in-ascension-logs').contains('chart your concept').click()
      cy.get('[role="dialog"]').should('be.visible')
      cy.get('[role="dialog"]').should('contain.text', 'Need a Spark of Inspiration?')

      // Close dialog
      cy.get('body').type('{esc}')
      cy.get('[role="dialog"]').should('not.exist')
    })
  })

  describe('Application Development Intro Section Forms', () => {
    it('opens ContactForm when clicking "Drop a line to start!"', () => {
      cy.get('.grid-in-application-development-intro').contains('Drop a line to start!').click()
      cy.get('[role="dialog"]').should('be.visible')
      cy.get('[role="dialog"]').should('contain.text', "Let's Build It!")

      // Close dialog
      cy.get('body').type('{esc}')
      cy.get('[role="dialog"]').should('not.exist')
    })
  })

  describe('Footer Forms', () => {
    it('opens ContactForm when clicking "Your Story"', () => {
      cy.get('footer').contains('Your Story').click()
      cy.get('[role="dialog"]').should('be.visible')
      cy.get('[role="dialog"]').should('contain.text', "Let's Build It!")

      // Close dialog
      cy.get('body').type('{esc}')
      cy.get('[role="dialog"]').should('not.exist')
    })
  })
})
