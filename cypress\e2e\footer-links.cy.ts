describe('All Links Verification', () => {
  beforeEach(() => {
    cy.visit('http://localhost:3000/')
  })

  it('verifies footer internal links', () => {
    cy.get('footer').contains('manystack').click()
    cy.url().should('include', '/')

    cy.get('footer').contains('Our Projects').click()
    cy.url().should('include', '/projects')
    cy.go('back')

    cy.get('footer').contains('Our Services').click()
    cy.url().should('include', '/services')
    cy.go('back')

    cy.get('footer').contains('Privacy Promise').click()
    cy.url().should('include', '/policies/privacy-promise')
    cy.go('back')

    cy.get('footer').contains('Friendly Terms').click()
    cy.url().should('include', '/policies/friendly-terms')
    cy.go('back')
  })

  it('verifies footer social media links', () => {
    // Verify Facebook link exists and has correct href
    cy.get('footer a[href="https://facebook.com/mnystck"]')
      .should('exist')
      .should('have.attr', 'href', 'https://facebook.com/mnystck')
      .should('have.attr', 'target', '_blank')

    // Verify X (Twitter) link exists and has correct href
    cy.get('footer a[href="https://x.com/mnystck"]')
      .should('exist')
      .should('have.attr', 'href', 'https://x.com/mnystck')
      .should('have.attr', 'target', '_blank')

    // Verify LinkedIn link exists and has correct href
    cy.get('footer a[href="https://www.linkedin.com/company/manystack"]')
      .should('exist')
      .should('have.attr', 'href', 'https://www.linkedin.com/company/manystack')
      .should('have.attr', 'target', '_blank')
  })

  it('verifies external links behavior without navigation', () => {
    // Test that clicking external links doesn't navigate away from current page
    // by intercepting the request and ensuring we stay on the same page

    // Intercept external requests to prevent actual navigation
    cy.intercept('GET', 'https://facebook.com/mnystck', { statusCode: 200 }).as('facebookRequest')

    // Get current URL before clicking
    cy.url().then(currentUrl => {
      // Click the Facebook link (it should open in new tab due to target="_blank")
      cy.get('footer a[href="https://facebook.com/mnystck"]').click()

      // Verify we're still on the same page (didn't navigate away)
      cy.url().should('eq', currentUrl)
    })

    // The request should not be intercepted because target="_blank" opens in new tab
    cy.get('@facebookRequest').should('not.have.been.called')
  })

  it('verifies footer email link functionality', () => {
    cy.get('footer').contains('Drop an email').should('exist')

    // Wait for email link to be built
    cy.wait(1000)

    cy.get('footer')
      .contains('Drop an email')
      .should('have.attr', 'href')
      .and('include', 'mailto:')
      .and('include', '<EMAIL>')
      .and('include', 'subject=My%20dream')
  })
})
