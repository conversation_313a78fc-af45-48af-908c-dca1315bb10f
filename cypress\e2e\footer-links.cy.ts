describe('All Links Verification', () => {
  beforeEach(() => {
    cy.visit('http://localhost:3000/')
  })

  it('verifies footer internal links', () => {
    cy.get('footer').contains('manystack').click()
    cy.url().should('include', '/')

    cy.get('footer').contains('Our Projects').click()
    cy.url().should('include', '/projects')
    cy.go('back')

    cy.get('footer').contains('Our Services').click()
    cy.url().should('include', '/services')
    cy.go('back')

    cy.get('footer').contains('Privacy Promise').click()
    cy.url().should('include', '/policies/privacy-promise')
    cy.go('back')

    cy.get('footer').contains('Friendly Terms').click()
    cy.url().should('include', '/policies/friendly-terms')
    cy.go('back')
  })

  it('verifies footer social media', () => {
    cy.get('footer a[href="https://facebook.com/mnystck"]').click()
    cy.url().should('include', 'https://facebook.com/mnystck')
    cy.go('back')

    cy.get('footer a[href="https://x.com/mnystck"]').click()
    cy.url().should('include', 'https://x.com/mnystck')
    cy.go('back')

    cy.get('footer a[href="https://www.linkedin.com/company/manystack"]').click()
    cy.url().should('include', 'https://www.linkedin.com/company/manystack')
    cy.go('back')
  })

  it('verifies footer email link functionality', () => {
    cy.get('footer').contains('Drop an email').should('exist')

    // Wait for email link to be built
    cy.wait(1000)

    cy.get('footer')
      .contains('Drop an email')
      .should('have.attr', 'href')
      .and('include', 'mailto:')
      .and('include', '<EMAIL>')
      .and('include', 'subject=My%20dream')
  })
})
