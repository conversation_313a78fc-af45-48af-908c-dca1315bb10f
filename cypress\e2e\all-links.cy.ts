describe('All Links Verification', () => {
  beforeEach(() => {
    cy.visit('http://localhost:3000/')
  })

  it('verifies content section links', () => {
    cy.get('.grid-in-service-accordion')
      .contains('service page')
      .should('have.attr', 'href', '/services')
  })

  it('verifies client feedback "See more" link works', () => {
    cy.get('.grid-in-carousel').contains('See more!').click()
    cy.url().should('include', '/client-feedback')
    cy.go('back')
  })

  it('verifies internal navigation by visiting them', () => {
    cy.get('footer').contains('Our Services').click()
    cy.url().should('include', '/services')
    cy.go('back')

    cy.get('footer').contains('Our Projects').click()
    cy.url().should('include', '/projects')
    cy.go('back')

    cy.get('.grid-in-service-accordion').contains('service page').click()
    cy.url().should('include', '/services')
    cy.go('back')
  })

  it('check that all external links have target="_blank"', () => {
    cy.get('a[href^="http"]').each($link => {
      const href = $link.attr('href')
      if (href && !href.includes('localhost')) {
        cy.wrap($link).should('have.attr', 'target', '_blank')
      }
    })
  })
})
